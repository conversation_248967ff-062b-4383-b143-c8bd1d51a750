import { Page } from "@playwright/test";
import fs from "fs";
import path from "path";

export class AuthenAction {
  private dataFilePath: string;
  constructor(private page: Page) {
    this.dataFilePath = path.resolve(__dirname, "../../data/authen-data.json");
  }

  async inputEmailAccount(email: string, password: string): Promise<void> {
    await this.page.getByRole("textbox", { name: "Email của bạn" }).type(email);
    await this.page.waitForTimeout(1000);
    await this.page.getByRole("textbox", { name: "<PERSON>ật khẩu" }).type(password);
    await this.page.waitForTimeout(1000);
  }

  async inputPhoneNumberAccount(
    phoneNumber: string,
    password: string
  ): Promise<void> {
    await this.page.getByText("Số điện thoại").click();
    await this.page
      .getByRole("textbox", { name: "<PERSON>ố điện thoại của bạn" })
      .type(phoneNumber);
    await this.page.getByRole("textbox", { name: "<PERSON>ậ<PERSON> khẩu" }).type(password);
  }

  async clickContinueToLogin(): Promise<void> {
    await this.page.getByRole("button", { name: "Tiếp tục" }).click();
    await this.page.waitForTimeout(1000);
  }

  async inputIdentifierAccount(
    companyName: string,
    identifier: string,
    password: string
  ): Promise<void> {
    await this.page
      .locator("div")
      .filter({ hasText: /^Tiếp tục với mã định danh$/ })
      .click();
    await this.page
      .getByRole("textbox", { name: "Tên tổ chức" })
      .type(companyName);
    await this.page
      .getByRole("textbox", { name: "Mã định danh" })
      .type(identifier);
    await this.page.getByRole("textbox", { name: "Mật khẩu" }).type(password);
  }

  async clickLogin(): Promise<void> {
    await this.page.getByRole("button", { name: "Đăng nhập" }).click();
  }

  async loginByMicrosoft(msEmail: string, password: string): Promise<void> {
    const page2Promise = this.page.waitForEvent("popup");
    await this.page
      .getByRole("button", { name: "Đăng nhập bằng Microsoft" })
      .click();
    const page2 = await page2Promise;
    await page2
      .getByRole("textbox", { name: "Enter your email, phone, or" })
      .fill(msEmail);
    await page2.getByRole("button", { name: "Next" }).click();
    await page2.getByRole("textbox", { name: "Password" }).fill(password);
    await page2.getByTestId("primaryButton").click();
    await page2.getByTestId("primaryButton").click();
  }

  async loginBySSO(ssoEmail: string, password: string): Promise<void> {
    await this.page.getByText("Đăng nhập một lần SSO").click();
    await this.page
      .getByRole("textbox", { name: "Email của bạn" })
      .fill(ssoEmail);
    await this.page.getByRole("button", { name: "Xác nhận" }).click();
    await this.page
      .getByRole("textbox", { name: "Enter your email, phone, or" })
      .fill(ssoEmail);
    await this.page.getByRole("button", { name: "Next" }).click();
    await this.page.locator("#i0118").fill(password);
    await this.page.getByRole("button", { name: "Sign in" }).click();
    await this.page.getByRole("button", { name: "Yes" }).click();
  }

  async logout(): Promise<void> {
    //await this.page.locator("#profile").click();
    await this.page
      .getByRole("button", { name: "icon-logout Đăng xuất" })
      .click();
  }

  async inputOldPassword(currentPassword: string): Promise<void> {
    await this.page
      .getByRole("textbox", { name: "Nhập mật khẩu cũ của bạn" })
      .fill(currentPassword);
  }

  async inputNewPassword(newPassword: string): Promise<void> {
    await this.page
      .getByRole("textbox", { name: "Nhập mật khẩu mới" })
      .fill(newPassword);
  }

  async inputConfirmPassword(newPassword: string): Promise<void> {
    await this.page
      .getByRole("textbox", { name: "Xác nhận mật khẩu mới" })
      .fill(newPassword);
  }

  async clickSavePassword(): Promise<void> {
    await this.page.getByRole("button", { name: "Lưu" }).click();
  }

  async clickCloseOnAlertPopup(): Promise<void> {
    await this.page.getByRole("button", { name: "Đóng" }).click();
  }

  async changePassword(): Promise<string> {
    // 1. Đọc password hiện tại
    const { change_pass_account } = this.getAuthData();
    const currentPassword = change_pass_account.password;

    // 2. Sinh password mới
    const newPassword = this.generateNextPassword(currentPassword);
    console.log(`Đang đổi mật khẩu: ${currentPassword} -> ${newPassword}`);

    // 3. Đổi trên web
    await this.inputOldPassword(currentPassword);
    await this.inputNewPassword(newPassword);
    await this.inputConfirmPassword(newPassword);
    await this.clickSavePassword();
    return newPassword;
  }

  saveNewPasswordToFile(newPass: string) {
    const data = this.getAuthData();
    data.change_pass_account.password = newPass;
    fs.writeFileSync(this.dataFilePath, JSON.stringify(data, null, 2));
  }

  private getAuthData() {
    return JSON.parse(fs.readFileSync(this.dataFilePath, "utf-8"));
  }

  // public saveAuthData(newPass: string) {
  //   const data = this.getAuthData();
  //   data.change_pass_account.password = newPass; // Lưu newPass vào password
  //   fs.writeFileSync(this.dataFilePath, JSON.stringify(data, null, 2));
  //   console.log("Đã update file authen-data.json");
  // }

  private generateNextPassword(oldPass: string) {
    const prefix = oldPass.match(/^[^\d]+/)?.[0] || "Gapo@";
    const num = parseInt(oldPass.replace(/^\D+/g, "")) || 0;
    return `${prefix}${num + 1}`;
  }
}

