import { Page, expect,Locator} from "@playwright/test";
import { time } from "console";
import path from 'path';
import taskData from "../../data/task-data.json";


export class TaskAction {
  private linkPath ='data/Images';
  constructor(private page: Page, private taskLocator?: Locator) {}
  private listUserLocator() {
    return this.page.locator('.selectable .title');
  }

  private listUserInTaskDetailLocator() {
    return this.page.locator('.member-wrapper .member-item');
  }
  async pressEscKey(){
   await this.page.keyboard.press('Escape');
  }
  //mytask
  async clickMyTask() {
    await this.page.getByRole("link", { name: "Công việc của tôi" }).click();
  }

  async clickCreateTaskBtn() {
    await this.page.getByRole("button", { name: "Tạo", exact: true }).click();
  }

  async fillTaskTitle(my_task_name: string) {
    await this.page.getByRole("textbox", { name: "<PERSON><PERSON><PERSON><PERSON> tiêu đề công việc..." }).fill(my_task_name);
  }

  async fillDescription(task_description : string){
    await this.page.waitForTimeout(500);
    await this.page.getByRole('textbox').filter({ hasText: /^$/ }).locator('div').nth(2).click();
    await this.page.getByRole('textbox').filter({ hasText: /^$/ }).first().fill(task_description);
  }

  async fillDescriptionEdited(task_description_edited: string) {
    const descriptionBox = this.page.locator('div[role="textbox"].public-DraftEditor-content');
    await descriptionBox.first().click();
    await descriptionBox.press('Control+A');
    await descriptionBox.press('Delete');
    await descriptionBox.fill(task_description_edited);
  }
  async addAssignee(){
    await this.page.waitForTimeout(500);
    await this.page.getByRole('button', { name: 'Thêm người thực hiện' }).click();
  }
  async addPriorityMedium(){
    await this.page.locator('div').filter({ hasText: /^Hoàn thành0$/ }).getByRole('button').nth(1).click();
    await this.page.getByTestId('popover').locator('div').filter({ hasText: 'Trung bình' }).nth(1).click();
  }
  async addPriorityHigh(){
    await this.page.locator('div').filter({ hasText: /^Hoàn thành0$/ }).getByRole('button').nth(1).click();
    await this.page.getByTestId('popover').locator('div').filter({ hasText: 'Cao' }).nth(1).click();
  }
  async searchAndSelectMembers(search_keyword : string){
    await this.page.waitForTimeout(1000)
    await this.page.getByRole('textbox', { name: 'Tìm kiếm' }).click();
    await this.page.waitForTimeout(1000)
    await this.page.getByRole('textbox', { name: 'Tìm kiếm' }).fill(search_keyword);
    await this.page.waitForTimeout(2000)
    const listUser = this.listUserLocator();
    await listUser.getByText(`${search_keyword} 04`, { exact: true }).click({timeout : 2000});
    await this.page.waitForTimeout(2000)
    await listUser.getByText(`${search_keyword} 03`, { exact: true }).click({timeout : 2000});
    await this.page.waitForTimeout(2000)
    await listUser.getByText(`${search_keyword} 02`, { exact: true }).click({timeout : 2000});
  }
  async searchAndSelectMembersInTaskDetail(search_keyword : string){
    await this.page.waitForTimeout(1000)
    await this.page.getByRole('textbox', { name: 'Tìm kiếm' }).click();
    await this.page.getByRole('textbox', { name: 'Tìm kiếm' }).fill(search_keyword);
    await this.page.waitForTimeout(2000)
    const listUser = this.listUserInTaskDetailLocator();
    await listUser.getByText(`${search_keyword} 04`, { exact: true }).click({timeout : 2000});
    await this.page.waitForTimeout(2000)
    await listUser.getByText(`${search_keyword} 03`, { exact: true }).click({timeout : 2000});
    await this.page.waitForTimeout(2000)
    await listUser.getByText(`${search_keyword} 02`, { exact: true }).click({timeout : 2000});
  }

  async addMemberGroup(search_keyword : string){
    await this.page.getByRole('textbox', { name: 'Tìm kiếm' }).click();
    await this.page.getByRole('textbox', { name: 'Tìm kiếm' }).fill(search_keyword);
    await this.page.waitForTimeout(2000)
    const listUser = this.listUserLocator();
    await listUser.getByText(`${search_keyword} 02`, { exact: true }).click({timeout : 2000});
  }

  async addDeadline(){
    await this.page.waitForTimeout(1000);
    await this.page.getByRole('button', { name: 'Thêm thời hạn' }).click();
    await this.page.waitForTimeout(1000);
    await this.page.locator('.react-datepicker__day--today').nth(1).click();
  }
  async addDeadlineEdited() {
    // Mở calendar
    await this.page.getByRole('button', { name: /5:00 PM.*Hôm nay/ }).click();

    // Lấy element của "today"
    const today = this.page.locator('.react-datepicker__day--today');

    // Chọn ngày hôm nay - 1 (ngay trước today)
    await today.locator('xpath=preceding-sibling::div[1]').nth(1).click();
  }
  
  //them tep dinh kem
  async addAttachments(){
    //await this.page.getByRole('button', { name: 'Thêm', exact: true }).click();
    const fileInput = this.page.locator('input[type="file"]').last();
    const filePath = taskData.expected_files.map(chooseFile =>
      path.resolve(`${this.linkPath}/${chooseFile}`)
    );
    // Upload file
    for (const file of filePath) {
      await fileInput.setInputFiles(file);
      await this.page.waitForTimeout(2000);
      //await this.page.locator('.gapo-ActionButton--transparent').last().click();
      //await this.page.waitForTimeout(2000);
    }
  }
  //Upload ở mục comment task
  async addAttachmentsEdited(){
    //await this.page.getByRole('button', { name: 'Thêm', exact: true }).click();
    const fileInput = this.page.locator('input[type="file"]').last();
    const filePath = taskData.expected_files_edited.map(chooseFile =>
      path.resolve(`${this.linkPath}/${chooseFile}`)
    );
    // Upload file
    for (const file of filePath) {
      await fileInput.setInputFiles(file);
      await this.page.waitForTimeout(2000);
      await this.page.locator('.gapo-ActionButton--transparent').last().click(); //comment task
      await this.page.waitForTimeout(2000);
    }
  }
  async clickSeeMoreFile(){
    await this.page.locator('span.text-show-more', { hasText: 'Xem thêm 2 tệp' }).click();
  }
  
  async clickOuterArea(){
    await this.page.locator('.spectrum-Underlay_eb7615').click();
  }
  async clickIconTag(){
    await this.page.locator('xpath=../following-sibling::span[contains(@class,"list-hover-action")]//button[.//span[normalize-space(text())="Nhãn dán"]]').click({timeout : 2000});
  }

  async clickAddTag() {
    // Locator nút "Thêm nhãn dán"
    const addTagButton = this.page.getByRole('button', { name: 'Thêm nhãn dán' });
    // Locator icon "+"
    const plusIcon = this.page.locator('div.plus-assignee-icon');

    if (await addTagButton.isVisible({ timeout: 2000 }).catch(() => false)) {
      // Nếu thấy "Thêm nhãn dán" thì click
      await addTagButton.click();
    } else {
      // Nếu không thấy thì hover task trước (nếu UI yêu cầu hover mới hiện icon)
      await this.page.locator('.task-item').first().hover();

      // Đợi icon "+" hiện
      await plusIcon.waitFor({ state: 'visible', timeout: 5000 });
      await plusIcon.click();
    }
  } 

  async addTags(tag_name:  string){
    await this.page.locator('.task-info__wrapper--tag button').click();
    await this.page.getByRole('button', { name: 'Tạo nhãn mới' }).click();
    await this.page.getByRole('textbox', { name: 'Nhập tên nhãn' }).fill(tag_name);
    await this.page.locator('span:nth-child(9)').click();//chọn màu
    await this.page.getByRole('button', { name: 'Tạo mới' }).click();
    await this.page.locator('.spectrum-Underlay_eb7615').click();
  }

  async clickSaveBtn() {
    await this.page.getByRole("button", { name: "Lưu" }).click();
    await this.page.waitForTimeout(3000);
  }
  async clickSubTaskSaveBtn(){
    await this.page.getByRole("button", { name: "Lưu" }).nth(1).click();
    await this.page.waitForTimeout(3000);
  }

  async clickArchivedBtn() {
    await this.page.getByRole("button", { name: "Lưu trữ" }).click();
    await this.page.waitForTimeout(3000);
  }
  //click btn dong
  async clickCloseBtn() {
    await this.page.getByRole("button", { name: "Đóng" }).click();
    await this.page.waitForTimeout(2000);
  }
  async listTaskByMyTask(my_task_name: string) {
    return this.page.locator("span.task-name-text", { hasText: my_task_name }).first();
  }

  async clickThreeDotMenuMyTask(){
    await this.page.locator("button.menu-task__action").first().click();
    // đợi animation chạy xong
    await this.page.waitForTimeout(2000);
  }
  async clickThreeDotMenu() { 
    await this.page.waitForTimeout(1000);
    await this.taskLocator!.hover(); 
    const menuBtn = this.taskLocator!.locator("button.menu-task__action"); 
   // await expect(menuBtn).toBeVisible({ timeout: 5000 }); 
    await menuBtn.click();
    await this.page.waitForTimeout(2000);
  }

  async clickDeleteBtn() {
    await this.page.locator('li[role="menuitem"][data-key="DELETE"]').click({ timeout: 3000 });
    const deleteBtn = this.page.getByRole("button", { name: "Xóa" });
    await deleteBtn.waitFor({ state: "visible", timeout: 5000 });
    await deleteBtn.click();
  }
  //btn xoa
  async clickDeleteProjectBtn() {
    await this.page.waitForTimeout(500);
    await this.page.getByText('Xóa').click({ timeout: 5000 });
    await this.page.getByRole('button', { name: 'Xóa' }).click({ timeout: 5000 });
  }

  async clickDuplicateBtn() {
    await this.page.locator('div.introjs-overlay').waitFor({ state: 'detached', timeout: 20000 });
    await this.page.getByText("Tạo bản sao").click({ timeout: 500 });
  }

  async clickViewDetailsBtn() {
    await this.page.getByText("Xem chi tiết", { exact: false }).click({ timeout: 500 });
  }

  //project
  async clickIconAtGeneralProject(){ 
    await this.page.waitForTimeout(500);
    await this.page.locator('button[class*="task-sidebar"]').click();
  }

  //icon + từng project //danh sách công việc trc khi sua case di chuyen 
  // async clickIconAtEachProject(){
  //   await this.page.locator("a.btn-option-add").first().click({ timeout: 5000 });
  // }
  async clickIconAtEachProject(project_name: string) {
    const projectRow = this.page.locator(
      `.task-tree-node-wrapper:has(span.task-tree-node-text:has-text("${project_name}"))`
   );
    await projectRow.locator("a.btn-option-add").click({ timeout: 5000 });
  }

  //chọn dscv or folder
  async clickChooseTaskListOrFolder() {
    await this.page.locator('//span[contains(@class, "ant-dropdown")]');
  }

  async clickChooseTaskList() {
    await this.page.locator('//span[text()="Thêm danh sách công việc"]').click();
  }

  async clickChooseFolder() {
    await this.page.locator('//span[text()="Thêm thư mục"]').click();
  }

  //fill ten project placeholder
  projectName() {
    return this.page.locator('input[placeholder="Đặt tên cho dự án..."]');
  }
  async clickProjectName(project_name: string) {
    await this.page.waitForTimeout(500);
    await this.projectName().type(project_name);
    await this.page.waitForTimeout(500);
  }
  async fillNewProjectName(project_name_edited: string) {
    await this.projectName().fill(project_name_edited
    );
  }

  async addMemberBtn() {
    await this.page.locator("button.btn.btn-add-member-project").click();
  }

  async chooseMemberToProject() {
    const member = this.page.locator('label:has(input[type="checkbox"].spectrum-Checkbox-input_4870fc)').first();
    const memberName = await member.textContent();
    await member.locator('input[type="checkbox"]').check();
  }

  async closeProjectModal() {
    await this.page.locator('//button[contains(@class, "styles_closeButton__QjmHU")]').click({timeout : 5000});
  }

  async clickDoneButton() {
    await this.page.getByRole("button", { name: "Xong" }).click();
  }

  async clickSaveProject() {
    await this.page.locator('//div[contains(@class, "modal-dialog")]//button[(text()="Lưu")]').click();
    await this.page.waitForTimeout(3000);
  }

  async clickEnterKey() {
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(500);
  }

  async clickEnterKeyTwice() {
    await this.page.waitForTimeout(1000);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(500);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(500);
  }

  //ba cham cuoi mỗi project
  optionBtn() {
    return this.page.locator("button.btn-option-edit");
  }
  //ba cham cuoi project
  async clickProjectOptionBtn() {
    await this.page.waitForTimeout(2000);
    await this.optionBtn().first().click({ timeout: 5000 });
  }
  //ba chấm cuối mỗi tasklist or folder
  async clickTaskListOrFolderOptionBtn(task_list_name: string)  {
    const wrapper = this.page.locator(`.task-tree-node-wrapper:has(span.task-tree-node-text:has-text("${task_list_name}"))`);
    const optionBtn = wrapper.locator("button.btn-option-edit");
    await wrapper.hover();
    await optionBtn.waitFor({ state: "visible", timeout: 5000 });
    await optionBtn.click();
  }
  //ba cham cuoi collab project
  async clickCollabProjectOption(collab_project_name: string) {
      await this.page.waitForTimeout(2000);
      const targetProject = await this.getTargetProject(collab_project_name);
      const optionBtn = targetProject.locator("button.btn-option-edit");
      await targetProject.hover();
      await optionBtn.waitFor({ state: "visible", timeout: 5000 });
      await optionBtn.click();
  }
    //Hàm chọn dự án collab
  private async getTargetProject(collab_project_name?: string) {
    const allProjects = this.page.locator(".task-tree-node-wrapper");
    if (!collab_project_name) {
      return allProjects.first();
    }
    const filtered = allProjects.filter({ hasText: collab_project_name });
    const count = await filtered.count();
    if (count > 0) {
      return filtered.first();
    }
    return allProjects.first();
  }
  //chọn project 
  async getProjectIdByName(project_name: string) {
    const project = this.page.locator("div.task-tree-node-wrapper").filter({ hasText: project_name });
    await expect(project).toBeVisible({ timeout: 10000 });

    const projectId = await project.getAttribute("id");
    if (!projectId) {
      throw new Error(`Không tìm thấy id cho project: ${project_name}`);
    }
    return projectId;
  }
  //chọn task list
  async getTaskListIdByName(task_list_name: string) {
    const taskList = this.page.locator("div.task-tree-node-wrapper").filter({ hasText: task_list_name });
    await expect(taskList).toBeVisible({ timeout: 10000 });
    const taskListId = await taskList.getAttribute("id");
    if (!taskListId) {
      throw new Error(`Không tìm thấy id cho task list: ${task_list_name}`);
    }
    return taskListId;
  }

  async goToProject(project_Id: string){
  await this.page.goto(`https://www.gapowork.vn/task/project/${project_Id}`);
  }

  async goToTaskList(task_list_Id: string, project_Id: string){
    await this.page.goto(`https://www.gapowork.vn/task/project/${project_Id}?taskListId=${task_list_Id}`);
  }
  
  async chooseProjectById(project_Id: string) {
    const project = this.page.locator(`div.task-tree-node-wrapper[id="${project_Id}"]`);
    await project.hover();
    //await project.click();
  }

  async chooseTaskListById(task_list_Id: string) {
    const taskList = this.page.locator(`div.task-tree-node-wrapper[id="${task_list_Id}"]`);
    await expect(taskList).toBeVisible(); 
    await taskList.hover();
    await taskList.click();
  }
  
  //Chọn task 
  async chooseTask(task_name: string)  {
     const taskLocator = this.page.locator(".task-item-wrapper", {has: this.page.locator(".task-name-text__main", { hasText: task_name }),
  });
   if(await taskLocator.count()>0){
      await taskLocator.first().hover();
      this.taskLocator = taskLocator.first();
    }else{
      const firstTask = this.page.locator('.task-name-text__main').first();
      await expect(firstTask).toBeVisible({ timeout: 20000 });
      await firstTask.hover();
      this.taskLocator = firstTask;
    }
  }

  private async createTaskInListTaskView(task_name: string) {
    await this.page.locator('.create-task-btn').first().click();
    await this.page.keyboard.type(task_name);
    await this.page.keyboard.press("Enter");
  }

  //chọn action chỉnh sửa
  async clickActionEdit() {
    await this.page.locator('div.gapo-Typography', { hasText: "Chỉnh sửa dự án" }).click({ timeout: 20000 });
  }

  //chọn action thêm thành viên vào dự án
  async clickActionAddMemberToProject() {
    await this.page.locator("div.gapo-Typography", { hasText: "Thêm thành viên vào dự án" }).click({ timeout: 8000, force: true });
  }

  //chọn action ẩn dự án
  async clickActionnHideProject() {
    await this.page.waitForTimeout(2000);
    await this.page.locator('//div[contains(@class,"gapo-Typography") and normalize-space(text())="Ẩn dự án"]').click({ force: true });
  }

  //chọn action luu trữ
  async clickActionArchived(){
    await this.page.locator("div.gapo-Typography", { hasText: "Lưu trữ dự án" }).click({ force: true, timeout: 5000 });
  }
  //Chọn project đã lưu trữ
  async chooseArchivedProject(archived_project_name: string) {
    const archivedProject = this.page.locator("div.task-tree-node-wrapper", { hasText: archived_project_name });
    await archivedProject.first().click();
  }
  //di den man archived
  async goToArchived(){
    await this.page.getByText('Lưu trữ').click();
    await this.page.waitForTimeout(1000);
  }
  //chọn action khoi phuc du an
  async clickActionRestore(){
    await this.page.getByText('Khôi phục dự án').click();
    await this.page.waitForTimeout(1000);
  }
  //click btn khoi phuc
  async clickRestoreBtn(){
    await this.page.getByRole('button', { name: 'Khôi phục' }).click();
    await this.page.waitForTimeout(2000);
  }
  //chọn action tạo bản sao
  async clickActionDuplicate() {
    await this.page.waitForTimeout(2000);
    const duplicateItem = this.page.locator('li[role="menuitem"][data-key="DUPLICATE"]');
    await duplicateItem.click();
  }
  // add task list
  async addTaskList(task_list_name : string)   {
    await this.page.locator('//input[@placeholder="Đặt tên cho danh sách công việc..."]').fill(task_list_name);
    await this.page.waitForTimeout(500);
  }

  //thêm folder
  async addFolder(folder_name: string){
    await this.page.locator('//input[@placeholder="Đặt tên cho thư mục..."]').fill(folder_name);
    await this.page.waitForTimeout(500);
  }
  //thêm subtask
async addSubTask(sub_task_name: string){
  await this.page.locator('button.btn-create-sub-task',).click();
  await this.page.waitForTimeout(1000);
  await this.page.locator('input#create-sub-task-form-').fill(sub_task_name);
  await this.page.keyboard.press("Enter");
  await this.page.waitForTimeout(2000);
}
 
  //chọn đúng task list
  async clickTaskList(task_list_name: string) {
    await this.page.locator("span.task-tree-node-text", { hasText: task_list_name }).click();
  }
  
  //create new task or creat new section // ấn btn Tạo có 2 loại tạo cv mới or tạo thư mục
  async clickCreateNewTaskBtn() {
    await this.page.getByText("Tạo công việc mới").click();
  }

async moveTaskAction(task_list_after_move: string, project_after_move: string, task_list_before_move: string) {
    const moveBtn = this.page.locator('.btn-move-task');
    await moveBtn.waitFor({ state: 'visible' });
    await moveBtn.click({ force: true });
    // Chờ cho aria-expanded = true
    await expect(moveBtn).toHaveAttribute('aria-expanded', 'true');
    //await this.page.waitForSelector('.task-tree-node', { state: 'visible', timeout: 2000 });
    await this.page.locator('.minitask-move-task-body button:has(.task-tree-node-text)', { hasText: project_after_move }).last().click({ force: true });
    await this.page.waitForTimeout(1000);
    await this.page.locator('.minitask-move-task-body button:has(.task-tree-node-text)', { hasText: task_list_after_move }).last().click();
    await this.page.waitForTimeout(1000);
  }
 
  //chat
  async clickCreatGroupChat()  {
    await this.page.locator('button[class*="spectrum-ActionButton--quiet"]:has(svg[fill="var(--spectrum-global-color-accentWorkSecondary)"])').click();
    await this.page.getByRole("menuitem", { name: "Tạo nhóm Chat Tạo nhóm Chat v" }).locator("div").nth(1).click({ timeout: 500 });
  }
  async clickDoneChat() {
    await this.page.getByRole("button", { name: "Xong" }).click();
  }
  async tabTask() {
    await this.page.getByLabel("collab-tab").getByText("Công việc").click({timeout:600});
  }
  async enableTaskCollab() {
    await this.page.getByRole("button", { name: "Kích hoạt chức năng công việc" }).click();
    await this.page.getByRole("button", { name: "Kích hoạt" }).click();
  }
  async showAProject() {
    await this.page.locator(".gapo-ActionButton.icon-header").first().click();
    await this.page.locator("input.spectrum-ToggleSwitch-input_3526dd").click();
  }
  async clickCollabProject(collab_project_name: string) {
      const targetProject = await this.getTargetProject(collab_project_name);
      await targetProject.click();
  }
  async disbandGroup() {
    await this.page.getByText('Giải tán nhóm').click();
    await this.page.locator('button').filter({ hasText: 'Giải tán nhóm' }).click();
    await this.page.waitForTimeout(5000);
  }
  async clickHeaderThread() { 
      const header = this.page.locator('#collab-header');
      const popup = this.page.locator('div[class*="spectrum-Popover--bottom"]');
      await expect(header).toBeVisible();
      await header.click();
      await expect(popup).toBeVisible({ timeout: 5000 });
      // try {
      //   await popup.waitFor({ state: 'hidden', timeout: 2000 }); 
      //   await header.click();
      //   await expect(popup).toBeVisible({ timeout: 5000 });
      // } catch {}
  }
  async openChatThread() {
    const threads = this.page.locator("a[class*='thread-item']");
    const count = await threads.count();

    for (let i = 0; i < count; i++) {
      const threadName = (await threads.nth(i).innerText()).trim();
      if (threadName.includes("Lưu trữ") || threadName.includes("GapoSystem")||threadName.includes("Timekeeping Chatbot")) {
        continue;
      }
      await threads.nth(i).click();
      return;
    }
  }

  async closeWarningModal() {
    await this.page.locator('.modal-dialog .btn-cancel').click();
    await this.page.locator('.modal-dialog').waitFor({ state: 'hidden', timeout: 5000 });
  }
}
