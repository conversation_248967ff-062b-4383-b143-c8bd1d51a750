import { Page, expect, Browser } from "@playwright/test";
export class TaskVerify {
  constructor(private page: Page) {}
  //verify task ở cvct
  async verifyMyTaskVisible(my_task_name: string) {
    await expect(this.page.getByRole('link', { name: '<PERSON><PERSON>ng việc của tôi' })).toBeVisible();
    await expect(this.page.locator('span.task-name-text', { hasText: my_task_name })).toBeVisible();
  }
  async verifyMyTaskNotExist(my_task_name: string) {
    await expect(this.page.locator('span.task-name-text', { hasText: my_task_name })).toHaveCount(0);
  }
  //verify dự án chung
  //old
  async verifyProjectVisible1(project_name: string) {
    // await expect(this.page.getByText(projectName)).toBeVisible({ timeout: 2000 });
    await this.page.locator('span.task-tree-node-text', { hasText: project_name }).waitFor({ state: 'visible', timeout: 20000 });
    await expect(this.page.getByText(project_name, { exact: false })).toBeVisible();
  }
  //new
  async verifyProjectVisible(project_name: string) {
  const projectNode = this.page
    .locator('span.task-tree-node-text')
    .filter({ hasText: new RegExp(`^${project_name}`) })
    .first(); // match đúng text, không dính partial

  await expect(projectNode).toBeVisible({ timeout: 20000 });
}

  async verifyCollabProjecttNotExist() { 
      const allProjects = this.page.locator(".task-tree-node-wrapper");
      const beforeCount = await allProjects.count();
      await expect(allProjects).toHaveCount(beforeCount - 1, { timeout: 5000 });
      await this.page.waitForTimeout(2000);
      const afterCount = await allProjects.count();
      await expect(afterCount).toBe(beforeCount - 1);
  }

  async verifyCollabProjectHideSuccess() {
    await expect(this.page.getByText('Ẩn dự án thành công')).toBeVisible({ timeout: 5000 });
  }

  async verifyEditProjectVisible(new_project_name: string) {
    await expect(this.page.locator('span.task-tree-node-text', { hasText: new_project_name })).toBeVisible();
  }

  async verifyProjectNotExist(new_project_name: string,project_name: string) {
    await expect(this.page.locator('span.task-tree-node-text', { hasText: new_project_name })).toHaveCount(0);
    await expect(this.page.locator('span.task-tree-node-text', { hasText: project_name })).toHaveCount(0);
  }

  //verify thêm thành viên vào dự án
  async verifyAddMemberVisible() {
    const memberCheckbox = this.page.locator('label:has(input[type="checkbox"].spectrum-Checkbox-input_4870fc) input[type="checkbox"]').first();
    const memberName = await memberCheckbox.locator('xpath=..').textContent();
    await expect(memberCheckbox).toBeChecked({ timeout: 5000 });
  }
    //verify danh sách công viec
  async verifyTaskListVisible(task_list_name: string) {
    await this.page.locator('div.task-tree-node-wrapper').first().click();
    await expect(this.page.locator('span.task-tree-node-text', { hasText: task_list_name })).toBeVisible();
  }

  //verify thư mục
  async verifyFolderVisible(folder_name: string) {
    await this.page.locator('div.task-tree-node-wrapper').first().click();
    await expect(this.page.locator('span.task-tree-node-text', { hasText: folder_name })).toBeVisible();
  }
  //verify tạo bản sao danh sách công việc
  async verifyDuplicateTaskListVisible(task_list_name: string) {
    await this.page.locator('div.task-tree-node-wrapper').first().click();
    const dup = `Bản sao của ${task_list_name}`;
    await expect(this.page.locator('span.task-tree-node-text', { hasText: dup })).toBeVisible();
    //click vào list task gốc
    await this.page.locator('span.task-tree-node-text').filter({ hasText: new RegExp(`^${task_list_name}$`) }).click();
    const originalTasks = this.page.locator('.task-item .task-name');
    const originalTexts = (await originalTasks.allTextContents()).map(t => t.trim());
    //click vào list task bản sao
    await this.page.locator('span.task-tree-node-text').filter({ hasText: new RegExp(`^${dup}$`) }).click();
    const clonedTasks = this.page.locator('.task-item .task-name');
    const clonedTexts = (await clonedTasks.allTextContents()).map(t => t.trim());
    //so sánh list task gốc và bản sao giống nhau => tạo passed
    expect(clonedTexts).toEqual(originalTexts);
  }

  //verify tạo bản sao folder
  async verifyDuplicateFolderVisible(folder_name: string) {
    const dupFolder = `Bản sao của ${folder_name}`;
    await expect(this.page.locator('span.task-tree-node-text', { hasText: dupFolder })).toBeVisible();
    //Lấy tất cả list trong folder gốc
    const originalFolder = this.page.locator('.task-tree-node-wrapper').filter({has: this.page.locator('span.task-tree-node-text', { hasText: new RegExp(`^${folder_name}$`) }),});
    await originalFolder.click();
    const originalLists = await originalFolder.locator('.task-tree-node-tasklist .task-tree-node-text').allTextContents();
    const originalListNames = originalLists.map(t => t.trim());
    //Lấy tất cả list trong folder bản sao
    const clonedFolder = this.page.locator('.task-tree-node-wrapper').filter({has: this.page.locator('span.task-tree-node-text', { hasText: new RegExp(`^${dupFolder}$`) }),});
    await clonedFolder.click();
    const clonedLists = await clonedFolder.locator('.task-tree-node-tasklist .task-tree-node-text').allTextContents();
    const clonedListNames = clonedLists.map(t => t.trim());
    //So sánh list trong gốc và bản sao
    expect(new Set(clonedListNames)).toEqual(new Set(originalListNames));
    //Với mỗi list → mở trong folder gốc và folder bản sao rồi so sánh task
    for (const listName of originalListNames) {
      // List gốc
      const originalList = originalFolder.locator('.task-tree-node-tasklist span.task-tree-node-text', {hasText: new RegExp(`^${listName}$`),});
      await originalList.click();
      const originalTasks = (await this.page.locator('.task-item .task-name').allTextContents()).map(t => t.trim());
      // List bản sao
      const clonedList = clonedFolder.locator('.task-tree-node-tasklist span.task-tree-node-text', {hasText: new RegExp(`^${listName}$`),});
      await clonedList.click();
      const clonedTasks = (await this.page.locator('.task-item .task-name').allTextContents()).map(t => t.trim());

      expect(new Set(clonedTasks)).toEqual(new Set(originalTasks));
    }
  }

  //verify tạo bản sao công việc
  async verifyDuplicateTaskVisible(task_name: string) {
    const dup =`Bản sao của ${task_name}`;
    await expect(this.page.locator('span.task-name-text', { hasText: dup })).toBeVisible({timeout: 20000});
    //verify tên + description task giống với task gốc
  }
  //verify task 
  async verifyTaskVisible(task_name: string) {
    await this.page.waitForTimeout(2000);
    await expect(this.page.locator('span.task-name-text').getByText(task_name, { exact: true })).toBeVisible({timeout: 20000});
  }
  async verifyNewTaskVisible(task_new_name: string) {
    await expect(this.page.locator('span.task-name-text').getByText(task_new_name, { exact: true })).toBeVisible({timeout: 30000});
  }
  async verifyDescriptionVisible(task_description: string) {
    await expect(this.page.locator('div[role="textbox"]').filter({ hasText: task_description })).toBeVisible({ timeout: 30000 });
  }

  async verifyDuplicateDescriptionVisible(task_name: string, task_description: string) {
    await this.page.locator('span.task-name-text', { hasText: `Bản sao của ${task_name}`}).click();
    await expect(this.page.locator('div[role="textbox"]').filter({ hasText: task_description })).toBeVisible({ timeout: 30000 });
  }
  async verifyTagsVisible(tag_name: string){
    await expect(
  this.page.getByRole("button", { name: tag_name, exact: true })
  ).toBeVisible();
  }
  async verifyDeadline(expected_deadline: string) {
    const deadline = this.page.locator("button.task-due-date-btn").last();
    await expect(deadline).toBeVisible();
    await expect(deadline).toContainText(expected_deadline);
  }
  async verifyDeadlineEdited(expected_deadline_edited: string) {
    const deadline = this.page.locator("button.task-due-date-btn").last();
    await expect(deadline).toBeVisible();
    await expect(deadline).toContainText(expected_deadline_edited);
  }
  async verifyPriorityMedium() {
    const priorityIcon = this.page.locator('.task-priority-btn svg.gapo-Icon').last();
    //const priorityIcon = this.page.locator('.task-priority-btn').last();
    await expect(priorityIcon).toHaveAttribute('fill', 'var(--spectrum-global-color-criticalPrimary)');
  }
  async verifyPriorityHigh() {
    const priorityIcon = this.page.locator('.task-priority-btn svg.gapo-Icon').last();
    await expect(priorityIcon).toHaveAttribute('fill', 'var(--spectrum-global-color-contentSecondary)');
    //await expect(priorityIcon).toHaveAttribute('fill', 'var(--spectrum-global-color-negativePrimary)');
  }
  private normalizeFileName(name: string) {
    return name
      .trim()
      .toLowerCase()
      .replace(/\.jpeg$/, '.jpg'); // chuẩn hóa về .jpg
  }

  async verifyUploadedFiles(expected_files: string[]) {
    // ✅ Chờ upload xong
    await this.page.locator('.file-item.is-loading').waitFor({ state: 'detached' });

    // ✅ Lấy danh sách() tên file hiển thị
    const actualFiles =(await this.page
      .locator('.file-item-inner .text-name')
      .allTextContents()).map(this.normalizeFileName);
    // Chuẩn hóa cả expectedFiles
    const normalizedExpected = expected_files.map(this.normalizeFileName);

    console.log('📂 Files hiển thị:', actualFiles);

    // ✅ Verify từng file mong đợi có trong actualFiles
    for (const expected of normalizedExpected) {
      const found = actualFiles.some(file => file.includes(expected));
      expect(found, `Không tìm thấy file: ${expected}`).toBeTruthy();
    }
  }
    async verifyUploadedFilesEdited(expected_files_edited: string[]) {
      // ✅ Chờ upload xong
    await this.page.locator('.file-item.is-loading').waitFor({ state: 'detached' });

    // ✅ Lấy danh sách() tên file hiển thị
    const actualFiles =(await this.page
      .locator('.file-item-inner .text-name')
      .allTextContents()).map(this.normalizeFileName);
    // Chuẩn hóa cả expectedFiles
    const normalizedExpected = expected_files_edited.map(this.normalizeFileName);

    console.log('📂 Files hiển thị:', actualFiles);

    // ✅ Verify từng file mong đợi có trong actualFiles
    for (const expected of normalizedExpected) {
      const found = actualFiles.some(file => file.includes(expected));
      expect(found, `Không tìm thấy file: ${expected}`).toBeTruthy();
    }
  }
}
