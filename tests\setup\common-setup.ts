import path from "path";
import fs from "fs";
import { AuthenAction } from "../../pages/authen/authen-action";
import { NavigateAction } from "../../pages/navigate/navigate-action";
import { getAccountData } from "../../helpers/getAccountData";
import { Browser } from "@playwright/test";

/**
 * Hàm login và lưu storage state vào file
 * @param browser Browser instance từ Playwright
 * @param env M<PERSON><PERSON> trường (prod, staging, uat, ...)
 * @param accountKey Tên key account trong getAccountData() (vd: "post_account", "survey_account")
 * @param storageFileName Tên file storage muốn lưu (vd: "prod_post_storageState.json")
 * @returns storagePath (đường dẫn file storage)
 */
export async function createStorageState(
  browser: Browser,
  env: string,
  accountKey: string,
  storageFileName: string
) {
  const storageDir = path.resolve("data", "storage");
  const storagePath = path.join(storageDir, storageFileName);

  // Tạo thư mục storage nếu chưa có
  fs.mkdirSync(storageDir, { recursive: true });

  // Tạo context login
  const tempContext = await browser.newContext();
  const tempPage = await tempContext.newPage();

  const authenAction = new AuthenAction(tempPage);
  const nav = new NavigateAction(tempPage);

  await nav.openUrl();

  const { email, password } = (getAccountData() as any)[accountKey];
  await authenAction.inputEmailAccount(email, password);
  await authenAction.clickContinueToLogin();
  await tempPage.waitForTimeout(2000);

  await tempContext.storageState({ path: storagePath });
  await tempContext.close();

  console.log(`✅ Saved storage for ${accountKey} → ${storagePath}`);

  return storagePath;
}
