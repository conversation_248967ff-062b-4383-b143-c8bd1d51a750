// tests/test-task.spec.ts
import { Page, test, BrowserContext } from "@playwright/test";
import { createStorageState } from "./setup/common-setup";
import { NavigateAction } from "../pages/navigate/navigate-action";
import { TaskAction } from "../pages/task/task-action";
import { TaskVerify } from "../pages/task/task-verify";
import taskData from "../data/task-data.json";
import { stringify } from "querystring";
import { create } from "domain";

let page: Page;
let context: BrowserContext;
let navAction: NavigateAction;
let taskAction: TaskAction;
let taskVerify: TaskVerify;

let page_1: Page;
let context_1: BrowserContext;
let navAction_1: NavigateAction;
let taskAction_1: TaskAction;
let taskVerify_1: TaskVerify;

const projectName = taskData.project_name + '_' + Math.floor(Math.random() * 1000).toString();
const projectNameEdited = taskData.project_name_edited + '_' + Math.floor(Math.random() * 1000).toString();
const projectArchived = taskData.archived_project_name + '_' + Math.floor(Math.random() * 1000).toString();
const projectBeforeMove = taskData.project_before_move + '_' + Math.floor(Math.random() * 1000).toString();
const projectAfterMove = taskData.project_after_move + '_' + Math.floor(Math.random() * 1000).toString();

const ENV = process.env.ENV || "prod";
test.describe("Task Workflow", () => {
  test.setTimeout(200000);
  test.beforeAll(async ({ browser }) => {
    // Storage cho User A
    test.setTimeout(120000);
    const storagePath = await createStorageState(
      browser,
      ENV,
      "task_account", // key account trong file {ENV}-accounts.json
      `${ENV}-task-storage-state.json`
    );

    // Mở context từ storage
    context = await browser.newContext({ storageState: storagePath });
    page = await context.newPage();
    navAction = new NavigateAction(page);
    taskAction = new TaskAction(page);
    taskVerify = new TaskVerify(page);

    // Điều hướng 1 lần
    await navAction.openUrl();
    
    // Storage cho User B
    const storagePath1 = await createStorageState(
      browser,
      ENV,
      "task_account_1", // key account B trong file {ENV}-accounts.json
      `${ENV}-task-storage-state_1.json`
    );
    context_1 = await browser.newContext({ storageState: storagePath1 });
    page_1 = await context_1.newPage();
    navAction_1 = new NavigateAction(page_1);
    taskAction_1 = new TaskAction(page_1);
    taskVerify_1 = new TaskVerify(page_1);
    await navAction_1.openUrl();

  });
  test.beforeEach(async () =>{
    await navAction.gotoTask();
  });
  test.afterAll(async () => {
    if (context) await context.close();
    if (context_1) await context_1.close();
      // await context?.close();
      // await context_1?.close();
          // Đảm bảo đóng context sau mỗi test
   
  });

  async function createProject(taskAction: any, navAction: any, project_name: string){
    await taskAction.clickIconAtGeneralProject();
    await taskAction.clickProjectName(project_name);
    await taskAction.addMemberBtn();  
    await taskAction.chooseMemberToProject();
    await navAction.acceptNotiPopup();
    await taskAction.clickDoneButton();
    await taskAction.clickSaveProject();
    await navAction.continueGuidelineOfPost();
  }

  async function createTaskList(taskAction: any, navAction: any, task_list_name: string,project_name: string){
    await taskAction.clickIconAtEachProject(project_name);
    await taskAction.clickChooseTaskListOrFolder();
    await taskAction.clickChooseTaskList();
    await taskAction.addTaskList(task_list_name);
    await taskAction.clickSaveBtn();
    await navAction.continueGuidelineOfPost();
  }

  async function createTask(taskAction: any, navAction: any, task_name: string, task_description: string, search_keyword: string){
    await taskAction.clickCreateTaskBtn();
    await taskAction.clickCreateNewTaskBtn();
    await taskAction.fillTaskTitle(task_name);
    await taskAction.fillDescription(task_description);
    await navAction.acceptNotiPopup();
    await page.waitForTimeout(1000);
    await taskAction.addAssignee();
    await taskAction.searchAndSelectMembersInTaskDetail(search_keyword);
    await taskAction.clickOuterArea();
    await taskAction.addPriorityMedium();
    await taskAction.addDeadline();
    await taskAction.addAttachments(); 
    await page.waitForTimeout(1000);
    await taskAction.clickSaveBtn();
  }

  async function accessProjectByName(taskAction: any, project_name: string){
    const projectId = await taskAction.getProjectIdByName(project_name);
    // dùng id này để mở lại project
    await taskAction.goToProject(projectId);
    await taskAction.chooseProjectById(projectId);
  }

  async function accessTaskListByName(taskAction: any,navAction: any, task_list_name: string){
    const taskListId = await taskAction.getTaskListIdByName(task_list_name);
    await taskAction.goToTaskList(taskListId);
    await navAction.continueGuidelineOfPost();
    await taskAction.chooseTaskListById(taskListId);
  } 
  
  async function addMemberToProject(taskAction: any, navAction: any, search_keyword: string){
    await taskAction.clickProjectOptionBtn();
    await taskAction.clickActionAddMemberToProject();
    await taskAction.searchAndSelectMembers(search_keyword);
    await navAction.acceptNotiPopup();
    await taskAction.clickDoneButton();
  }
  async function deleteTasks(taskAction: any, navAction: any, task_name: string,project_name : string, task_list_name: string){ 
    await accessProjectByName(taskAction, project_name);
    await accessTaskListByName(taskAction,navAction, task_list_name);
    await taskAction.chooseTask(task_name);
    await taskAction.clickThreeDotMenu();
    await taskAction.clickDeleteBtn();
  }

  test("@createProject Create new project", async () => {
    await createProject(taskAction, navAction, projectName);
    await page.waitForTimeout(1000);
    await taskVerify.verifyProjectVisible(projectName);
  });

  test("@addMember Add member to project", async () =>{
    //await createProject(taskAction, navAction, taskData.project_name);
    await page.waitForTimeout(1000);
    await accessProjectByName(taskAction, projectName);
    await addMemberToProject(taskAction, navAction, taskData.search_keyword);
    await page.waitForTimeout(1000);
    //user B verify
    await page_1.waitForTimeout(1000);
    await navAction_1.gotoTask();
    await taskVerify_1.verifyProjectVisible(projectName);

  });

  test("@createTaskList Create new task list", async () => {
    //await createProject(taskAction, navAction, taskData.project_name);
    await accessProjectByName(taskAction, projectName);
    await page.waitForTimeout(1000);
    await createTaskList(taskAction, navAction, taskData.task_list_name,projectName);
    await taskVerify.verifyTaskListVisible(taskData.task_list_name);
  });

  test("@createFolder Create new folder", async () =>{
    //await createProject(taskAction, navAction, taskData.project_name);
    await accessProjectByName(taskAction, projectName);
    await page.waitForTimeout(1000);
    await taskAction.clickIconAtEachProject(projectName);
    await taskAction.clickChooseTaskListOrFolder();
    await taskAction.clickChooseFolder();
    await taskAction.addFolder(taskData.folder_name);
    await taskAction.clickSaveBtn();
    await navAction.continueGuidelineOfPost();
    await taskVerify.verifyFolderVisible(taskData.folder_name);
  });
  
  test("@createTask Create task", async () => {
    await accessProjectByName(taskAction, projectName);
    await accessTaskListByName(taskAction,navAction, taskData.task_list_name);
    await page.waitForTimeout(1000);
    await createTask(taskAction, navAction, taskData.task_name, taskData.task_description, taskData.search_keyword);
    //user B verify
    await navAction_1.gotoTask();
    await accessProjectByName(taskAction_1, projectName);
    await page_1.waitForTimeout(1000);
    await accessTaskListByName(taskAction_1,navAction_1, taskData.task_list_name);
    await page_1.waitForTimeout(2000);
    await navAction_1.continueGuidelineOfPost();
    await taskVerify_1.verifyTaskVisible(taskData.task_name);
    await taskAction_1.chooseTask(taskData.task_name);
    await taskAction_1.clickThreeDotMenu();
    await taskAction_1.clickViewDetailsBtn();
    await taskVerify_1.verifyDescriptionVisible(taskData.task_description);
    await taskVerify_1.verifyPriorityMedium();
    await taskVerify_1.verifyDeadline(taskData.expected_deadline);
    await taskVerify_1.verifyUploadedFiles(taskData.expected_files);
    //Xóa task
    await page.waitForTimeout(1000);
    await deleteTasks(taskAction, navAction, taskData.task_name,projectName, taskData.task_list_name);
  });

  test("@addtagfortask Add tags to task", async () =>{
    await accessProjectByName(taskAction, projectName);
    await accessTaskListByName(taskAction,navAction, taskData.task_list_name);
    await page.waitForTimeout(1000);
    await createTask(taskAction, navAction, taskData.task_name, taskData.task_description, taskData.search_keyword);
    await page.waitForTimeout(1000);
    //await page_1.waitForTimeout(2000);
    await taskAction.chooseTask(taskData.task_name);
    await taskAction.clickThreeDotMenu();
    await taskAction.clickViewDetailsBtn();
    await navAction.acceptNotiPopup();
    await taskAction.addTags(taskData.tag_name);
    await navAction.clickIconClear();

    await taskAction.chooseTask(taskData.task_name);
    await taskVerify.verifyTagsVisible(taskData.tag_name);
    //Xóa task
    await page.waitForTimeout(1000);
    await deleteTasks(taskAction, navAction, taskData.task_name,projectName, taskData.task_list_name);
  })

  test("@editTask Edit task", async () => {
    // await accessProjectByName(taskAction, taskData.project_name);
    // await accessTaskListByName(taskAction,navAction, taskData.task_list_name);
    // await page.waitForTimeout(1000);
    await accessProjectByName(taskAction, projectName);
    await accessTaskListByName(taskAction,navAction, taskData.task_list_name);
    await page.waitForTimeout(1000);
    await createTask(taskAction, navAction, taskData.task_name, taskData.task_description, taskData.search_keyword);
    await page.waitForTimeout(1000);
    await taskAction.chooseTask(taskData.task_name);
    await taskAction.clickThreeDotMenuMyTask();
    await taskAction.clickViewDetailsBtn();
    await taskAction.fillTaskTitle(taskData.task_name_edited);
    await taskAction.fillDescription(taskData.task_description_edited);
    await page.waitForTimeout(1000);
    await taskAction.addPriorityHigh();
    await taskAction.addAttachmentsEdited();
    await taskAction.addDeadlineEdited();
    await taskAction.pressEscKey();

    await accessProjectByName(taskAction, projectName);
    await page.waitForTimeout(1000);
    await accessTaskListByName(taskAction,navAction, taskData.task_list_name);
    await page.waitForTimeout(2000);
    await navAction.continueGuidelineOfPost();
    await taskVerify.verifyTaskVisible(taskData.task_name_edited);
    await taskVerify.verifyDeadlineEdited(taskData.expected_deadline_edited);
    await taskVerify.verifyPriorityHigh();
    await taskAction.chooseTask(taskData.task_name_edited);
    await taskAction.clickThreeDotMenuMyTask();
    await taskAction.clickViewDetailsBtn();
    await taskVerify.verifyDescriptionVisible(taskData.task_description);
    await taskAction.clickSeeMoreFile();
    await taskVerify.verifyUploadedFilesEdited(taskData.expected_files_edited);
    //Xóa task
    await deleteTasks(taskAction, navAction, taskData.task_name,projectName, taskData.task_list_name);
  });

  test("@createSubtask Create new subtask", async () =>{
    await accessProjectByName(taskAction, projectName);
    await accessTaskListByName(taskAction,navAction, taskData.task_list_name);
    await page.waitForTimeout(1000);
    await createTask(taskAction, navAction, taskData.task_name_edited, taskData.task_description, taskData.search_keyword);
    await page.waitForTimeout(1000);
    await taskAction.chooseTask(taskData.task_name_edited);
    await taskAction.clickThreeDotMenu();
    await taskAction.clickViewDetailsBtn();
    await navAction.acceptNotiPopup();
    await taskAction.addSubTask(taskData.sub_task_name);
    await navAction.clickIconClear();
    await taskAction.chooseTask(taskData.sub_task_name);
    await taskAction.clickThreeDotMenu();
    await taskAction.clickViewDetailsBtn();
    await taskAction.fillDescription(taskData.task_description_edited);
    await navAction.acceptNotiPopup();
    await taskAction.addPriorityMedium();
    await taskAction.addDeadline();
    await taskAction.addAttachmentsEdited();
    await page.waitForTimeout(1000);
    await navAction.clickIconClear();
    await taskAction.clickSubTaskSaveBtn();
    //await context?.close();
    //user B verify
    await page_1.waitForTimeout(1000);
    await navAction_1.gotoTask();
    await accessProjectByName(taskAction_1, projectName);
    await page_1.waitForTimeout(1000);
    await accessTaskListByName(taskAction_1,navAction_1, taskData.task_list_name);
    await page_1.waitForTimeout(2000);
    await navAction_1.continueGuidelineOfPost();
    await taskVerify_1.verifyTaskVisible(taskData.sub_task_name);
    await taskAction_1.chooseTask(taskData.sub_task_name);
    await taskAction_1.clickThreeDotMenu();
    await taskAction_1.clickViewDetailsBtn();
    await taskVerify_1.verifyDescriptionVisible(taskData.task_description_edited);
    await taskVerify_1.verifyPriorityMedium();
    await taskVerify_1.verifyDeadline(taskData.expected_deadline);
    await taskVerify_1.verifyUploadedFilesEdited(taskData.expected_files_edited);
    //Xóa task
    await deleteTasks(taskAction, navAction, taskData.task_name_edited,projectName, taskData.task_list_name);
  })

  test("@duplicateTask Create duplicate tasks", async () => {
    await accessProjectByName(taskAction, projectName);
    await accessTaskListByName(taskAction,navAction, taskData.task_list_name);
    await page.waitForTimeout(1000);
    await createTask(taskAction, navAction, taskData.task_name, taskData.task_description, taskData.search_keyword);
    await page.waitForTimeout(1000);
    await taskAction.chooseTask(taskData.task_name);
    await taskAction.clickThreeDotMenu();
    await taskAction.clickDuplicateBtn();
    await navAction.clickIconClear();

    await taskVerify.verifyDuplicateTaskVisible(taskData.task_name);
    await taskVerify.verifyDuplicateDescriptionVisible(taskData.task_name,taskData.task_description);
    await deleteTasks(taskAction, navAction, taskData.task_name,projectName, taskData.task_list_name);
  });

  test("@duplicateTaskList Create duplicate task list", async ()=>{
    await accessProjectByName(taskAction, projectName);
    await taskAction.clickTaskListOrFolderOptionBtn(taskData.task_list_name);
    await taskAction.clickActionDuplicate();
    await page.waitForTimeout(2000);
    await navAction.continueGuidelineOfPost();
    await taskVerify.verifyDuplicateTaskListVisible(taskData.task_list_name);
  });

  test("@duplicateFolder Create duplicate folder", async ()=>{
    await accessProjectByName(taskAction, projectName);
    await taskAction.clickTaskListOrFolderOptionBtn(taskData.folder_name);
    await taskAction.clickActionDuplicate();
    await page.waitForTimeout(2000);
    await navAction.continueGuidelineOfPost();
    await taskVerify.verifyDuplicateFolderVisible(taskData.folder_name);
  });

  test("@editProject Edit project", async () => {
    await accessProjectByName(taskAction, projectName);
    await taskAction.clickProjectOptionBtn();
    await taskAction.clickActionEdit();
    await taskAction.fillNewProjectName(projectNameEdited);
    await taskAction.addMemberBtn();
    await taskAction.chooseMemberToProject();
    await navAction.acceptNotiPopup();
    await taskAction.clickDoneButton();
    await taskAction.clickSaveProject();
    await taskVerify.verifyEditProjectVisible(projectNameEdited);
  });

  test("@createMyTask Create new my task", async () => {
    await taskAction.clickMyTask();
    await taskAction.clickCreateTaskBtn();
    await taskAction.fillTaskTitle(taskData.my_task_name);
    await taskAction.clickSaveBtn();
    await taskVerify.verifyMyTaskVisible(taskData.my_task_name);
  });

  test("@deleteMyTask Delete my task ", async () => {
    await taskAction.listTaskByMyTask(taskData.my_task_name);
    await taskAction.clickThreeDotMenuMyTask();
    await taskAction.clickDeleteBtn();
    await taskVerify.verifyMyTaskNotExist(taskData.my_task_name);
  });

  test("@deleteProject Delete this project", async () => {
    await accessProjectByName(taskAction, projectNameEdited);
    await taskAction.clickProjectOptionBtn();
    await taskAction.clickDeleteProjectBtn();
    await taskVerify.verifyProjectNotExist(projectName, projectNameEdited);
  });

  test("@archivedProject Archived project",async () =>{
    await taskAction.clickIconAtGeneralProject();
    await taskAction.clickProjectName(projectArchived);
    await taskAction.clickSaveProject();
    await navAction.continueGuidelineOfPost();
    await accessProjectByName(taskAction, projectArchived);
    await taskAction.clickProjectOptionBtn();
    await taskAction.clickActionArchived();
    await taskAction.clickArchivedBtn();
    await taskAction.clickCloseBtn();
    await taskAction.goToArchived();
    await taskVerify.verifyProjectVisible(projectArchived);
  });

  test("@restoreProject Restore project", async() =>{
    await taskAction.goToArchived();
    await page.waitForTimeout(1000);
    await taskAction.chooseArchivedProject(projectArchived);
    await taskAction.clickProjectOptionBtn();
    await taskAction.clickActionRestore();
    await taskAction.clickRestoreBtn();
    await page.waitForTimeout(1000);
    await taskAction.clickCloseBtn();
    await navAction.gotoTask();
    await page.waitForTimeout(2000);

    await taskVerify.verifyProjectVisible(projectArchived);
    await accessProjectByName(taskAction, projectArchived);
    await taskAction.clickProjectOptionBtn();
    await taskAction.clickDeleteProjectBtn();
  });

  test("@moveTasks Move Tasks", async () =>{
    await createProject(taskAction, navAction, projectAfterMove);
    await accessProjectByName(taskAction, projectAfterMove);
    await createTaskList(taskAction, navAction, taskData.task_list_after_move,projectAfterMove);
    await page.waitForTimeout(1000);

    await createProject(taskAction, navAction, projectBeforeMove);
    await accessProjectByName(taskAction, projectBeforeMove);
    await createTaskList(taskAction, navAction, taskData.task_list_before_move, projectBeforeMove);
    await accessProjectByName(taskAction, projectBeforeMove);
    await addMemberToProject(taskAction, navAction, taskData.search_keyword);
    await page.waitForTimeout(1000);

    await accessProjectByName(taskAction, projectBeforeMove);
    await accessTaskListByName(taskAction,navAction, taskData.task_list_before_move);
    await page.waitForTimeout(1000);
    await createTask(taskAction, navAction, taskData.task_move, taskData.task_description, taskData.search_keyword);
    await taskAction.chooseTask(taskData.task_move);
    await taskAction.clickThreeDotMenu();
    await taskAction.clickViewDetailsBtn();
    await navAction.acceptNotiPopup();
    await taskAction.moveTaskAction(taskData.task_list_after_move, projectAfterMove, taskData.task_list_before_move );
    await navAction.clickIconClear();
    //Verify
    await accessProjectByName(taskAction, projectAfterMove);
    await accessTaskListByName(taskAction,navAction, taskData.task_list_after_move);
    await page.waitForTimeout(1000);
    await navAction.continueGuidelineOfPost();
    await taskVerify.verifyTaskVisible(taskData.task_move);
    await taskAction.chooseTask(taskData.task_move);
    await taskAction.clickThreeDotMenu();
    await taskAction.clickViewDetailsBtn();
    await taskVerify.verifyDescriptionVisible(taskData.task_description);
    await taskVerify.verifyPriorityMedium();
    await taskVerify.verifyDeadline(taskData.expected_deadline);
    await taskVerify.verifyUploadedFiles(taskData.expected_files);
    await accessProjectByName(taskAction, projectBeforeMove);
    await taskAction.clickProjectOptionBtn();
    await taskAction.clickDeleteProjectBtn();
    await accessProjectByName(taskAction, projectAfterMove);
    await taskAction.clickProjectOptionBtn();
    await taskAction.clickDeleteProjectBtn();
  });

  test("@collabCreate Create a collaborative project", async () => {
    await navAction.gotoChat();
    await taskAction.clickEnterKeyTwice();
    await taskAction.clickCreatGroupChat();
    await taskAction.addMemberGroup(taskData.search_keyword);
    await navAction.acceptNotiPopup();
    await taskAction.clickDoneChat();
    await navAction.clickbtnKP();
    await page.waitForTimeout(2000);
    await taskAction.tabTask();
    await taskAction.enableTaskCollab();
    await navAction.continueGuidelineOfPost();
    await taskAction.showAProject();
    await page.waitForTimeout(2000);
    //User B verify
    await page_1.waitForTimeout(10000);
    await navAction_1.gotoTask();
    await taskVerify_1.verifyProjectVisible(taskData.collab_project_name);
  });

  test("@collabHide Hide project", async() =>{
    await page.waitForTimeout(2000);
    await taskAction.clickCollabProject(taskData.collab_project_name);
    await taskAction.clickCollabProjectOption(taskData.collab_project_name);
    await taskAction.clickActionnHideProject();

    await taskVerify.verifyCollabProjectHideSuccess();

    // Đang mở project đó sau khi ẩn đi sẽ báo lỗi nội dung ko tồn tại
    await taskAction.closeWarningModal();
    //giải tán nhóm chat sau khi tạo dự án collab
    await navAction.gotoChat();
    await page.waitForTimeout(1000);
    await navAction.continueGuidelineOfPost();
    await taskAction.openChatThread();
    await navAction.handleGuidePopup();
    await taskAction.clickHeaderThread();
    await taskAction.disbandGroup();
  });
});