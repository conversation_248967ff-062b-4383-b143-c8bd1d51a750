# Project Name Management

## V<PERSON><PERSON> đề
Tr<PERSON><PERSON><PERSON> đâ<PERSON>, mỗi lần chạy test case sẽ sinh ra project name kh<PERSON><PERSON> nhau do sử dụng `Math.random()`, điều này gây khó khăn trong việc debug và theo dõi test.

## Giải pháp

### 1. Sử dụng ProjectNameManager (Khuyến nghị)

ProjectNameManager tạo ra project name cố định trong suốt một test session bằng cách sử dụng timestamp hoặc environment variable.

```typescript
import { ProjectNameManager } from "./utils/project-name-manager";

const projectName = ProjectNameManager.generateProjectName(taskData.project_name);
// Kết quả: "Project Automation_1703123456789"
```

### 2. <PERSON><PERSON><PERSON> cách sử dụng

#### Cách 1: Sử dụng timestamp tự động (mặc định)
```bash
npm test
```
Project names sẽ có format: `ProjectName_1703123456789`

#### Cách 2: Sử dụng custom session ID
```bash
TEST_SESSION_ID=test_001 npm test
```
Project names sẽ có format: `ProjectName_test_001`

#### Cách 3: Sử dụng readable session ID
```typescript
// Trong test setup
ProjectNameManager.setSessionId(ProjectNameManager.generateReadableSessionId());
```
Project names sẽ có format: `ProjectName_20231221_143045`

### 3. API Reference

#### `generateProjectName(baseName: string): string`
Tạo project name với session ID

#### `resetSession(): void`
Reset session ID về timestamp mới

#### `getSessionId(): string`
Lấy session ID hiện tại

#### `setSessionId(id: string): void`
Set session ID cụ thể

#### `generateReadableSessionId(): string`
Tạo session ID dạng YYYYMMDD_HHMMSS

### 4. Lợi ích

- **Cố định**: Project names giống nhau trong suốt test session
- **Unique**: Mỗi test session có ID khác nhau
- **Flexible**: Có thể control từ environment variable
- **Readable**: Có thể tạo ID dễ đọc với timestamp
- **Debug-friendly**: Dễ dàng trace và debug test

### 5. Migration

Thay vì:
```typescript
const projectName = taskData.project_name + '_' + Math.floor(Math.random() * 1000).toString();
```

Sử dụng:
```typescript
const projectName = ProjectNameManager.generateProjectName(taskData.project_name);
```
