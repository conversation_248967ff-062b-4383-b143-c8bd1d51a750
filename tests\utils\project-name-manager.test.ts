// tests/utils/project-name-manager.test.ts
import { test, expect } from "@playwright/test";
import { ProjectNameManager } from "./project-name-manager";

test.describe("ProjectNameManager", () => {
  test("should generate consistent project names within same session", () => {
    const baseName1 = "Project Test";
    const baseName2 = "Another Project";
    
    const name1_first = ProjectNameManager.generateProjectName(baseName1);
    const name2_first = ProjectNameManager.generateProjectName(baseName2);
    
    // Gọi lại lần nữa để kiểm tra consistency
    const name1_second = ProjectNameManager.generateProjectName(baseName1);
    const name2_second = ProjectNameManager.generateProjectName(baseName2);
    
    // Cùng base name phải tạo ra cùng project name
    expect(name1_first).toBe(name1_second);
    expect(name2_first).toBe(name2_second);
    
    // Khác base name phải có cùng session ID
    const sessionId = ProjectNameManager.getSessionId();
    expect(name1_first).toBe(`${baseName1}_${sessionId}`);
    expect(name2_first).toBe(`${baseName2}_${sessionId}`);
  });
  
  test("should use environment variable when available", () => {
    const originalEnv = process.env.TEST_SESSION_ID;
    
    try {
      // Set environment variable
      process.env.TEST_SESSION_ID = "custom_test_id";
      
      // Reset để pick up env variable
      ProjectNameManager.resetSession();
      
      const projectName = ProjectNameManager.generateProjectName("Test Project");
      expect(projectName).toBe("Test Project_custom_test_id");
      expect(ProjectNameManager.getSessionId()).toBe("custom_test_id");
      
    } finally {
      // Restore original environment
      if (originalEnv) {
        process.env.TEST_SESSION_ID = originalEnv;
      } else {
        delete process.env.TEST_SESSION_ID;
      }
      ProjectNameManager.resetSession();
    }
  });
  
  test("should generate readable session ID", () => {
    const readableId = ProjectNameManager.generateReadableSessionId();
    
    // Format should be YYYYMMDD_HHMMSS
    expect(readableId).toMatch(/^\d{8}_\d{6}$/);
    
    // Should be current date/time (within reasonable range)
    const now = new Date();
    const year = now.getFullYear().toString();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    
    expect(readableId.startsWith(`${year}${month}${day}`)).toBe(true);
  });
  
  test("should allow custom session ID", () => {
    const customId = "my_custom_session_123";
    
    ProjectNameManager.setSessionId(customId);
    
    const projectName = ProjectNameManager.generateProjectName("Test");
    expect(projectName).toBe(`Test_${customId}`);
    expect(ProjectNameManager.getSessionId()).toBe(customId);
    
    // Reset for other tests
    ProjectNameManager.resetSession();
  });
});
