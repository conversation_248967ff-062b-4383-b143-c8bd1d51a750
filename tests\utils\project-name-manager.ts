// tests/utils/project-name-manager.ts
export class ProjectNameManager {
  private static sessionId: string = this.initializeSessionId();

  /**
   * Khởi tạo session ID từ environment variable hoặc timestamp
   */
  private static initializeSessionId(): string {
    // Ưu tiên sử dụng TEST_SESSION_ID từ environment variable
    const envSessionId = process.env.TEST_SESSION_ID;
    if (envSessionId) {
      return envSessionId;
    }

    // Fallback về timestamp
    return Date.now().toString();
  }

  /**
   * Tạo project name cố định cho test session
   * @param baseName - Tên base từ data file
   * @returns Project name với session ID
   */
  static generateProjectName(baseName: string): string {
    return `${baseName}_${this.sessionId}`;
  }

  /**
   * Reset session ID (dùng khi cần tạo session mới)
   */
  static resetSession(): void {
    this.sessionId = this.initializeSessionId();
  }

  /**
   * Lấy session ID hiện tại
   */
  static getSessionId(): string {
    return this.sessionId;
  }

  /**
   * Set session ID cụ thể (dùng cho testing hoặc debug)
   */
  static setSessionId(id: string): void {
    this.sessionId = id;
  }

  /**
   * Tạo session ID từ thời gian hiện tại với format dễ đọc
   * Format: YYYYMMDD_HHMMSS
   */
  static generateReadableSessionId(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}_${hours}${minutes}${seconds}`;
  }
}
